#!/usr/bin/env python3
"""
产品图片API测试脚本
用于测试图片查看功能的API接口
"""

import requests
import json
import os
from config import PRODUCT_IMAGES_BASE_PATH

def test_product_images_api():
    """测试产品图片API"""
    
    # 测试配置
    base_url = "http://localhost:5000"  # 根据实际情况修改
    test_sn = "HSE2517001001"
    
    print("=== 产品图片API测试 ===")
    print(f"基础URL: {base_url}")
    print(f"测试SN: {test_sn}")
    print(f"图片存储路径: {PRODUCT_IMAGES_BASE_PATH}")
    
    # 检查图片目录是否存在
    sn_dir = os.path.join(PRODUCT_IMAGES_BASE_PATH, test_sn)
    print(f"\n检查图片目录: {sn_dir}")
    
    if os.path.exists(sn_dir):
        files = os.listdir(sn_dir)
        image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'))]
        print(f"找到 {len(image_files)} 个图片文件: {image_files}")
    else:
        print("图片目录不存在，请先运行 setup_product_images.py")
        return
    
    # 测试获取图片列表API
    print(f"\n=== 测试图片列表API ===")
    try:
        url = f"{base_url}/api/product-test-query/images/{test_sn}"
        print(f"请求URL: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('success'):
                images = data.get('data', [])
                print(f"成功获取 {len(images)} 张图片")
                
                # 测试获取具体图片文件
                if images:
                    test_image = images[0]
                    filename = test_image['name']
                    print(f"\n=== 测试图片文件API ===")
                    print(f"测试图片: {filename}")
                    
                    file_url = f"{base_url}/api/product-test-query/images/{test_sn}/file/{filename}"
                    print(f"请求URL: {file_url}")
                    
                    file_response = requests.get(file_url, timeout=10)
                    print(f"文件响应状态码: {file_response.status_code}")
                    
                    if file_response.status_code == 200:
                        print(f"文件大小: {len(file_response.content)} 字节")
                        print(f"Content-Type: {file_response.headers.get('Content-Type', 'unknown')}")
                        print("✅ 图片文件API测试成功")
                    else:
                        print(f"❌ 图片文件API测试失败: {file_response.text}")
            else:
                print(f"❌ API返回失败: {data.get('message', '未知错误')}")
        else:
            print(f"❌ 请求失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求异常: {e}")
        print("请确保应用服务器正在运行")
    
    # 测试不存在的SN
    print(f"\n=== 测试不存在的SN ===")
    try:
        nonexistent_sn = "NONEXISTENT123"
        url = f"{base_url}/api/product-test-query/images/{nonexistent_sn}"
        print(f"请求URL: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if not data.get('success'):
                print(f"✅ 正确处理不存在的SN: {data.get('message')}")
            else:
                print(f"⚠️  意外的成功响应: {data}")
        else:
            print(f"响应: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求异常: {e}")

def check_directory_structure():
    """检查目录结构"""
    print("\n=== 检查目录结构 ===")
    
    if not os.path.exists(PRODUCT_IMAGES_BASE_PATH):
        print(f"❌ 基础目录不存在: {PRODUCT_IMAGES_BASE_PATH}")
        print("请运行: python3 setup_product_images.py")
        return False
    
    print(f"✅ 基础目录存在: {PRODUCT_IMAGES_BASE_PATH}")
    
    # 检查示例目录
    sample_sns = ["HSE2517001001", "HSE2517001002", "HSE2517001003"]
    
    for sn in sample_sns:
        sn_dir = os.path.join(PRODUCT_IMAGES_BASE_PATH, sn)
        if os.path.exists(sn_dir):
            files = os.listdir(sn_dir)
            image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'))]
            print(f"✅ {sn}: {len(image_files)} 个图片文件")
        else:
            print(f"⚠️  {sn}: 目录不存在")
    
    return True

if __name__ == "__main__":
    print("产品图片功能测试脚本")
    print("=" * 50)
    
    # 检查目录结构
    if check_directory_structure():
        # 测试API
        test_product_images_api()
    
    print("\n" + "=" * 50)
    print("测试完成")
    
    print("\n使用说明:")
    print("1. 确保应用服务器正在运行 (python3 app.py)")
    print("2. 如果目录不存在，请先运行: python3 setup_product_images.py")
    print("3. 在浏览器中访问成品测试查询页面进行功能测试")
