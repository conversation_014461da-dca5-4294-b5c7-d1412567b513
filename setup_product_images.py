#!/usr/bin/env python3
"""
产品图片目录设置脚本
用于创建产品图片存储目录结构和测试数据
"""

import os
import shutil
from config import PRODUCT_IMAGES_BASE_PATH

def setup_product_images_directory():
    """设置产品图片目录结构"""
    
    print(f"正在创建产品图片存储目录: {PRODUCT_IMAGES_BASE_PATH}")
    
    # 确保基础目录存在
    os.makedirs(PRODUCT_IMAGES_BASE_PATH, exist_ok=True)
    
    # 创建一些示例SN号的目录
    sample_sns = [
        "HSE2517001001",
        "HSE2517001002", 
        "HSE2517001003",
        "HSE2517002001",
        "HSE2517002002"
    ]
    
    for sn in sample_sns:
        sn_dir = os.path.join(PRODUCT_IMAGES_BASE_PATH, sn)
        os.makedirs(sn_dir, exist_ok=True)
        print(f"创建目录: {sn_dir}")
        
        # 创建一个README文件说明
        readme_path = os.path.join(sn_dir, "README.txt")
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(f"产品图片目录 - SN: {sn}\n")
            f.write("请将该SN号对应的产品图片放置在此目录下\n")
            f.write("支持的图片格式: .jpg, .jpeg, .png, .gif, .bmp, .webp\n")
            f.write(f"创建时间: {os.path.getctime}\n")
    
    print(f"\n产品图片目录设置完成!")
    print(f"基础路径: {PRODUCT_IMAGES_BASE_PATH}")
    print(f"已创建 {len(sample_sns)} 个示例SN目录")
    print("\n使用说明:")
    print("1. 将产品图片按SN号分类存放在对应目录下")
    print("2. 图片文件名建议使用描述性名称，如: 正面图.jpg, 背面图.jpg, 测试结果.png 等")
    print("3. 系统会自动扫描目录下的图片文件并在详情页面中显示")

def create_sample_image_placeholder():
    """创建示例图片占位符"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # 为第一个示例SN创建占位图片
        sn = "HSE2517001001"
        sn_dir = os.path.join(PRODUCT_IMAGES_BASE_PATH, sn)
        
        # 创建示例图片
        sample_images = [
            ("产品正面图.jpg", "产品正面", (800, 600)),
            ("产品背面图.jpg", "产品背面", (800, 600)),
            ("测试结果图.png", "测试结果", (1024, 768)),
            ("包装图.jpg", "产品包装", (600, 400))
        ]
        
        for filename, text, size in sample_images:
            img = Image.new('RGB', size, color='lightgray')
            draw = ImageDraw.Draw(img)
            
            # 尝试使用默认字体
            try:
                font = ImageFont.truetype("arial.ttf", 40)
            except:
                font = ImageFont.load_default()
            
            # 计算文本位置
            text_bbox = draw.textbbox((0, 0), text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            
            x = (size[0] - text_width) // 2
            y = (size[1] - text_height) // 2
            
            draw.text((x, y), text, fill='black', font=font)
            
            # 添加SN号
            sn_text = f"SN: {sn}"
            sn_bbox = draw.textbbox((0, 0), sn_text, font=font)
            sn_width = sn_bbox[2] - sn_bbox[0]
            draw.text((size[0] - sn_width - 20, 20), sn_text, fill='darkblue', font=font)
            
            # 保存图片
            img_path = os.path.join(sn_dir, filename)
            img.save(img_path)
            print(f"创建示例图片: {img_path}")
            
        print(f"\n为SN {sn} 创建了 {len(sample_images)} 张示例图片")
        
    except ImportError:
        print("警告: 未安装PIL库，无法创建示例图片")
        print("可以手动将图片文件放置到对应的SN目录中进行测试")

if __name__ == "__main__":
    print("=== 产品图片目录设置 ===")
    setup_product_images_directory()
    
    print("\n=== 创建示例图片 ===")
    create_sample_image_placeholder()
    
    print("\n=== 设置完成 ===")
    print("现在可以在成品测试查询页面中测试图片查看功能了!")
