from flask import Blueprint, jsonify, request
from database.db_manager import DatabaseManager
from datetime import datetime
from services.assembly_service import AssemblyService
from models.test_result import CPUTest, CouplerTest
from sqlalchemy import union_all, desc, text, literal, or_, asc
from sqlalchemy.orm import Query
from typing import List, Optional
import logging
import os
from config import PRODUCT_IMAGES_BASE_PATH

# 添加日志配置
logger = logging.getLogger(__name__)

product_test_query_bp = Blueprint('product_test_query', __name__)

class ProductTestQueryBuilder:
    """查询构建器"""
    def __init__(self, session):
        self.session = session
        # CPU查询
        self.cpu_query = self.session.query(
            CPUTest.id.label('id'),
            CPUTest.work_order.label('work_order'),
            CPUTest.pro_sn.label('pro_sn'),
            CPUTest.pro_code.label('pro_code'),  # 确保这个字段被包含在查询中
            CPUTest.pro_model.label('pro_model'),
            CPUTest.pro_status.label('pro_status'),
            CPUTest.tester.label('tester'),
            CPUTest.test_time.label('test_time'),
            CPUTest.test_status.label('test_status'),
            CPUTest.pro_batch.label('pro_batch'),
            CPUTest.rework.label('rework'),
            CPUTest.maintenance.label('maintenance'),
            CPUTest.remarks.label('remarks'),
            CPUTest.work_qty.label('work_qty'),
            CPUTest.comparison_result.label('comparison_result'),
            CPUTest.comparison_time.label('comparison_time'),
            CPUTest.comparison_user.label('comparison_user'),
            CPUTest.m_area_test_log.label('m_area_test_log'),  # 添加M区测试日志字段
            literal('CPU').label('product_type')
        )
        # 耦合器查询
        self.coupler_query = self.session.query(
            CouplerTest.id.label('id'),
            CouplerTest.work_order.label('work_order'),
            CouplerTest.pro_sn.label('pro_sn'),
            CouplerTest.pro_code.label('pro_code'),  # 确保这个字段被包含在查询中
            CouplerTest.pro_model.label('pro_model'),
            CouplerTest.pro_status.label('pro_status'),
            CouplerTest.tester.label('tester'),
            CouplerTest.test_time.label('test_time'),
            CouplerTest.test_status.label('test_status'),
            CouplerTest.pro_batch.label('pro_batch'),
            CouplerTest.rework.label('rework'),
            CouplerTest.maintenance.label('maintenance'),
            CouplerTest.remarks.label('remarks'),
            CouplerTest.work_qty.label('work_qty'),
            CouplerTest.comparison_result.label('comparison_result'),
            CouplerTest.comparison_time.label('comparison_time'),
            CouplerTest.comparison_user.label('comparison_user'),
            literal(None).label('m_area_test_log'),  # 耦合器无M区日志，使用NULL占位符
            CouplerTest.product_type.label('product_type')
        )
    
    def with_order_number(self, order_number: Optional[str]) -> 'ProductTestQueryBuilder':
        if order_number:
            self.cpu_query = self.cpu_query.filter(CPUTest.work_order.like(f'%{order_number}%'))
            self.coupler_query = self.coupler_query.filter(CouplerTest.work_order.like(f'%{order_number}%'))
        return self
    
    def with_serial_number(self, serial_number: Optional[str]) -> 'ProductTestQueryBuilder':
        if serial_number:
            self.cpu_query = self.cpu_query.filter(CPUTest.pro_sn.like(f'%{serial_number}%'))
            self.coupler_query = self.coupler_query.filter(CouplerTest.pro_sn.like(f'%{serial_number}%'))
        return self
    
    def with_product_code(self, product_code: Optional[str]) -> 'ProductTestQueryBuilder':
        """添加产品编码查询条件"""
        if product_code:
            self.cpu_query = self.cpu_query.filter(CPUTest.pro_code.like(f'%{product_code}%'))
            self.coupler_query = self.coupler_query.filter(CouplerTest.pro_code.like(f'%{product_code}%'))
        return self
    
    def with_test_result(self, test_result: Optional[str]) -> 'ProductTestQueryBuilder':
        if test_result:
            mapped_result = 'pass' if test_result == 'pass' else 'ng'
            self.cpu_query = self.cpu_query.filter(CPUTest.test_status == mapped_result)
            self.coupler_query = self.coupler_query.filter(CouplerTest.test_status == mapped_result)
        return self
    
    def with_date_range(self, start_date: Optional[str], end_date: Optional[str]) -> 'ProductTestQueryBuilder':
        if start_date:
            self.cpu_query = self.cpu_query.filter(CPUTest.test_time >= start_date)
            self.coupler_query = self.coupler_query.filter(CouplerTest.test_time >= start_date)
        if end_date:
            end_datetime = f"{end_date} 23:59:59"
            self.cpu_query = self.cpu_query.filter(CPUTest.test_time <= end_datetime)
            self.coupler_query = self.coupler_query.filter(CouplerTest.test_time <= end_datetime)
        return self

    def build(self, sort_field: Optional[str] = None, sort_order: Optional[str] = None) -> Query:
        """构建最终查询，支持动态排序
        
        Args:
            sort_field: 排序字段，对应数据库列名
            sort_order: 排序顺序，'asc'或'desc'
        """
        # 使用子查询来保证所有列都被正确包含
        subquery = self.cpu_query.union_all(self.coupler_query).subquery()
        
        # 创建最终查询
        query = self.session.query(subquery)
        
        # 根据排序参数设置排序条件
        if sort_field:
            # 字段名映射，前端字段名到数据库字段名的映射
            field_mapping = {
                'productCode': 'pro_code',
                'productStatus': 'pro_status',
                'maintenanceCount': 'maintenance',
                'reworkCount': 'rework',
                'testTime': 'test_time',
                'comparisonResult': 'comparison_result'
            }
            
            # 获取实际数据库字段名
            db_field = field_mapping.get(sort_field)
            
            if db_field:
                # 确定排序方向
                is_desc = sort_order == 'desc'
                
                # 应用排序条件
                if is_desc:
                    query = query.order_by(desc(text(db_field)))
                else:
                    query = query.order_by(asc(text(db_field)))
                
                logger.info(f"应用排序: {db_field} {'降序' if is_desc else '升序'}")
            else:
                # 如果没有找到映射字段，使用默认排序
                logger.warning(f"未识别的排序字段: {sort_field}，使用默认排序")
                query = query.order_by(text('test_time DESC'))
        else:
            # 默认按测试时间降序排序
            query = query.order_by(text('test_time DESC'))
        
        return query

@product_test_query_bp.route('/search', methods=['GET'])
def search_product_test():
    try:
        # 获取查询参数
        order_number = request.args.get('orderNumber')
        serial_number = request.args.get('serialNumber')
        product_code = request.args.get('productCode')
        test_result = request.args.get('testResult')
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        
        # 获取分页参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        
        # 获取排序参数
        sort_field = request.args.get('sortField')
        sort_order = request.args.get('sortOrder', 'desc')
        
        # 记录请求参数
        logger.info(f"查询参数: orderNumber={order_number}, serialNumber={serial_number}, "
                   f"productCode={product_code}, testResult={test_result}, "
                   f"startDate={start_date}, endDate={end_date}, "
                   f"page={page}, pageSize={page_size}, "
                   f"sortField={sort_field}, sortOrder={sort_order}")

        db = DatabaseManager()
        with db.get_session() as session:
            # 使用查询构建器
            query = (ProductTestQueryBuilder(session)
                    .with_order_number(order_number)
                    .with_serial_number(serial_number)
                    .with_product_code(product_code)
                    .with_test_result(test_result)
                    .with_date_range(start_date, end_date)
                    .build(sort_field, sort_order))
            
            # 先获取总记录数
            total_count = query.count()
            
            # 应用分页
            if page_size != -1:  # -1 表示不分页
                query = query.offset((page - 1) * page_size).limit(page_size)
            
            # 获取当前页的数据
            results = query.all()
            
            # 格式化结果
            data = [format_test_result(result) for result in results]
            
            return jsonify({
                'success': True,
                'data': data,
                'totalCount': total_count,
                'page': page,
                'pageSize': page_size if page_size != -1 else total_count,
                'message': f'找到 {total_count} 条记录'
            })

    except Exception as e:
        logger.error(f"查询失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'查询失败：{str(e)}'
        }), 500

@product_test_query_bp.route('/detail/<serial_number>', methods=['GET'])
def get_product_detail(serial_number):
    try:
        db = DatabaseManager()
        with db.get_session() as session:
            # 查询 CPU 控制器
            cpu_result = session.query(CPUTest).filter(
                CPUTest.pro_sn == serial_number
            ).order_by(CPUTest.test_time.desc()).first()

            # 查询耦合器/IO模块
            coupler_result = session.query(CouplerTest).filter(
                CouplerTest.pro_sn == serial_number
            ).order_by(CouplerTest.test_time.desc()).first()

            if cpu_result:
                return jsonify({
                    'success': True,
                    'data': format_cpu_detail(cpu_result),
                    'productType': 'CPU控制器'
                })
            elif coupler_result:
                return jsonify({
                    'success': True,
                    'data': format_coupler_detail(coupler_result),
                    'productType': coupler_result.display_type
                })
            else:
                return jsonify({
                    'success': False,
                    'message': f'未找到SN号为 {serial_number} 的产品记录'
                }), 404

    except Exception as e:
        print(f"Error in get_product_detail: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取详情失败：{str(e)}'
        }), 500

def get_pcba_binding_info(session, sn=None, order_number=None):
    """获取PCBA绑定信息"""
    try:
        service = AssemblyService(session)
        
        # 优先使用SN号查询
        if sn:
            result = service.search_assembly(sn)
            if result:
                return {
                    'assemblyId': result.get('assembly_id'),
                    'boardASN': result.get('board_a_sn'),
                    'boardBSN': result.get('board_b_sn'),
                    'boardCSN': result.get('board_c_sn'),
                    'boardDSN': result.get('board_d_sn'),
                    'boardTester': result.get('board_tester_name'),
                    'boardAssembledTime': result.get('created_at'),
                    'productSN': result.get('product_sn'),
                    'shellTester': result.get('complete_tester_name'),
                    'shellAssembledTime': result.get('assembled_at')
                }
        
        # 如果没有找到或没有SN号，使用工单号查询
        if order_number:
            results = service.search_by_order_number(order_number)
            if results and len(results) > 0:
                result = results[0]
                return {
                    'assemblyId': result.get('assembly_id'),
                    'boardASN': result.get('board_a_sn'),
                    'boardBSN': result.get('board_b_sn'),
                    'boardCSN': result.get('board_c_sn'),
                    'boardDSN': result.get('board_d_sn'),
                    'boardTester': result.get('board_tester_name'),
                    'boardAssembledTime': result.get('created_at'),
                    'productSN': result.get('product_sn'),
                    'shellTester': result.get('complete_tester_name'),
                    'shellAssembledTime': result.get('assembled_at')
                }
        
        return None
    except Exception as e:
        print(f"获取PCBA绑定信息失败: {str(e)}")
        return None

def format_cpu_detail(row):
    """格式化CPU控制器的详细信息"""
    try:
        with DatabaseManager().get_session() as session:
            # 获取PCBA绑定信息
            pcba_info = get_pcba_binding_info(session, sn=row.pro_sn) or \
                       get_pcba_binding_info(session, order_number=row.work_order)
            
            # 确保所有字段都有默认值
            basic_info = {
                'orderNumber': row.work_order or '-',
                'productModel': row.pro_model or '-',
                'productCode': row.pro_code or '-',
                'serialNumber': row.pro_sn or '-',
                'productStatus': {
                    1: '新品',
                    2: '维',
                    3: '返工'
                }.get(row.pro_status, '未知'),
                'tester': row.tester or '-',
                'testTime': row.test_time.strftime('%Y-%m-%d %H:%M:%S') if row.test_time else '-',
                'testResult': row.test_status or '-',
                'productBatch': row.pro_batch or '-',
                'workQty': getattr(row, 'work_qty', 0),
                'reworkCount': row.rework or 0,
                'maintenanceCount': row.maintenance or 0,
                'remarks': row.remarks or '-',
                'comparisonUser': row.comparison_user or '-',
                'comparisonTime': row.comparison_time.strftime('%Y-%m-%d %H:%M:%S') if row.comparison_time else '-',
                'test_auto_info': getattr(row, 'test_auto_info', '{}')  # 新增：auto标记信息
            }
            
            device_info = {
                'deviceName': getattr(row, 'device_name', '-'),
                'serialNumber': getattr(row, 'serial', '-'),
                'softwareVersion': getattr(row, 'sw_version', '-'),
                'buildDate': getattr(row, 'build_date', '-'),
                'backplaneVersion': getattr(row, 'back_ver', '-'),
                'highSpeedPulse': getattr(row, 'high_speed_io_version', '-')
            }
            
            test_results = {
                'backplane': getattr(row, 'backplane', 0),
                'bodyIO': getattr(row, 'body_io', 0),
                'ledTube': getattr(row, 'led_tube', 0),
                'ledBulb': getattr(row, 'led_bulb', 0),
                'netPort': getattr(row, 'net_port', 0),
                'rs485_1': getattr(row, 'rs485_1', 0),
                'rs485_2': getattr(row, 'rs485_2', 0),
                'rs232': getattr(row, 'rs232', 0),
                'canbus': getattr(row, 'canbus', 0),
                'ethercat': getattr(row, 'ethercat', 0),
                'usbDrive': getattr(row, 'usb_drive', 0),
                'sdSlot': getattr(row, 'sd_slot', 0),
                'debugPort': getattr(row, 'debug_port', 0),
                'dipSwitch': getattr(row, 'dip_switch', 0),
                'resetBtn': getattr(row, 'reset_btn', 0)
            }
            
            result = {
                'basicInfo': basic_info,
                'deviceInfo': device_info,
                'testResults': test_results,
                'pcbaBindingInfo': pcba_info or {},
                'mAreaTestLog': {
                    'hasLog': getattr(row, 'm_area_test_log', None) is not None and getattr(row, 'm_area_test_log', None) != {},
                    'logData': getattr(row, 'm_area_test_log', None)
                }  # 新增：M区测试日志信息
            }
            
            print("Detail data:", result)
            return result
            
    except Exception as e:
        print(f"格式化CPU控制器详情失败: {str(e)}")
        return {
            'basicInfo': {},
            'deviceInfo': {},
            'testResults': {},
            'pcbaBindingInfo': {}
        }

def format_coupler_detail(row):
    """格式化耦合器和IO模块的详细信息"""
    try:
        with DatabaseManager().get_session() as session:
            # 获取PCBA绑定信息
            pcba_info = get_pcba_binding_info(session, sn=row.pro_sn) or \
                       get_pcba_binding_info(session, order_number=row.work_order)
            
            basic_info = {
                'orderNumber': row.work_order or '-',
                'productModel': row.pro_model or '-',
                'productCode': row.pro_code or '-',
                'serialNumber': row.pro_sn or '-',
                'productStatus': {
                    1: '新品',
                    2: '维修',
                    3: '返工'
                }.get(row.pro_status, '未知'),
                'tester': row.tester or '-',
                'testTime': row.test_time.strftime('%Y-%m-%d %H:%M:%S') if row.test_time else '-',
                'testResult': row.test_status or '-',
                'productBatch': row.pro_batch or '-',
                'workQty': getattr(row, 'work_qty', 0),
                'reworkCount': row.rework or 0,
                'maintenanceCount': row.maintenance or 0,
                'remarks': row.remarks or '-',
                'comparisonUser': row.comparison_user or '-',
                'comparisonTime': row.comparison_time.strftime('%Y-%m-%d %H:%M:%S') if row.comparison_time else '-'
            }
            
            # 根据产品类型设置不同的设备信息字段名
            device_info = {}
            if row.product_type == 'io_module':
                device_info = {
                    'couplerVersion': row.couplersw_version or '-',  # IO软件版本
                    'couplerBuildDate': row.coupler_date or '-'      # IO构建日期
                }
            else:  # 耦合器
                device_info = {
                    'couplerVersion': row.couplersw_version or '-',
                    'couplerBuildDate': row.coupler_date or '-'
                }
            
            test_results = {
                'backplane': getattr(row, 'backplane', 0),
                'bodyIO': getattr(row, 'body_io', 0),
                'ledTube': getattr(row, 'led_tube', 0),
                'ledBulb': getattr(row, 'led_bulb', 0),
                'netPort': getattr(row, 'net_port', 0)
            }
            
            return {
                'basicInfo': basic_info,
                'deviceInfo': device_info,  # 确保设备信息被正确返回
                'testResults': test_results,
                'pcbaBindingInfo': pcba_info or {},
                'productType': row.display_type  # 确保产品类型被正确返回
            }
    except Exception as e:
        print(f"格式化耦合器/IO模块详情失败: {str(e)}")
        return {
            'basicInfo': {},
            'deviceInfo': {},
            'testResults': {},
            'pcbaBindingInfo': {}
        }

def format_test_result(row):
    """格式化测试结果"""
    try:
        product_type = getattr(row, 'product_type', '')
        
        # 添加详细的调试日志
        logger.debug(f"Raw row data: {row}")
        logger.debug(f"Row attributes: {dir(row)}")  # 查看所有可用属性
        logger.debug(f"Product code value: {getattr(row, 'pro_code', '-')}")
        
        # 检查是否有M区测试日志（仅CPU控制器有）
        has_m_area_log = False
        if product_type == 'CPU':
            m_area_log = getattr(row, 'm_area_test_log', None)
            has_m_area_log = m_area_log is not None and m_area_log != {} and m_area_log != ''
        
        formatted_result = {
            'productType': 'CPU控制器' if product_type == 'CPU' else 
                          'IO模块' if product_type == 'io_module' else '耦合器',
            'orderNumber': str(getattr(row, 'work_order', '-')),
            'serialNumber': str(getattr(row, 'pro_sn', '-')),
            'productCode': str(getattr(row, 'pro_code', '-')),
            'productModel': str(getattr(row, 'pro_model', '-')),
            'productStatus': {
                1: '新品',
                2: '维修',
                3: '返工'
            }.get(getattr(row, 'pro_status', None), '未知'),
            'tester': str(getattr(row, 'tester', '-')),
            'testTime': getattr(row, 'test_time', None).strftime('%Y-%m-%d %H:%M:%S') if getattr(row, 'test_time', None) else '-',
            'testResult': str(getattr(row, 'test_status', '-')),
            'productBatch': str(getattr(row, 'pro_batch', '-')),
            'reworkCount': int(getattr(row, 'rework', 0)),
            'maintenanceCount': int(getattr(row, 'maintenance', 0)),
            'remarks': str(getattr(row, 'remarks', '-')),
            'workQty': int(getattr(row, 'work_qty', 0)),
            'comparisonResult': str(getattr(row, 'comparison_result', None) or '-'),
            'comparisonTime': (getattr(row, 'comparison_time', None).strftime('%Y-%m-%d %H:%M:%S') 
                             if getattr(row, 'comparison_time', None) else '-'),
            'comparisonUser': str(getattr(row, 'comparison_user', None) or '-'),
            'hasMAreaLog': has_m_area_log  # 新增：M区日志标识
        }
        
        # 打印最终格式化的结果
        logger.debug(f"Formatted result: {formatted_result}")
        return formatted_result
        
    except Exception as e:
        logger.error(f"格式化测试结果失败: {str(e)}", exc_info=True)
        return {
            'productType': '未知',
            'orderNumber': '-',
            'serialNumber': '-',
            'productCode': '-',
            'productModel': '-',
            'productStatus': '未知',
            'tester': '-',
            'testTime': '-',
            'testResult': '-',
            'productBatch': '-',
            'reworkCount': 0,
            'maintenanceCount': 0,
            'remarks': '-',
            'workQty': 0,
            'comparisonResult': '-',
            'comparisonTime': '-',
            'comparisonUser': '-',
            'hasMAreaLog': False  # 新增：M区日志标识默认值
        }

@product_test_query_bp.route('/api/product-test-query/images/<serial_number>', methods=['GET'])
def get_product_images(serial_number):
    """获取指定SN号的产品图片"""
    try:
        if not serial_number:
            return jsonify({
                'success': False,
                'message': 'SN号不能为空'
            }), 400

        # 这里可以根据实际的图片存储方式来实现
        # 目前先返回一个示例响应，您可以根据实际需求修改

        # 示例：从文件系统或数据库中查找图片
        images = []

        # 方案1：如果图片存储在固定目录下，按SN号组织
        from flask import url_for

        # 使用配置文件中的图片存储路径
        image_dir = os.path.join(PRODUCT_IMAGES_BASE_PATH, serial_number)

        if os.path.exists(image_dir) and os.path.isdir(image_dir):
            # 支持的图片格式
            supported_formats = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}

            for filename in os.listdir(image_dir):
                file_path = os.path.join(image_dir, filename)
                if os.path.isfile(file_path):
                    # 检查文件扩展名
                    _, ext = os.path.splitext(filename.lower())
                    if ext in supported_formats:
                        # 获取文件信息
                        stat = os.stat(file_path)

                        images.append({
                            'name': filename,
                            'url': f'/api/product-test-query/images/{serial_number}/file/{filename}',
                            'size': stat.st_size,
                            'uploadTime': datetime.fromtimestamp(stat.st_mtime).isoformat()
                        })

        # 方案2：如果没有找到图片，返回提示信息
        if not images:
            return jsonify({
                'success': False,
                'message': f'未找到SN号 {serial_number} 的相关图片'
            })

        return jsonify({
            'success': True,
            'data': images,
            'message': f'找到 {len(images)} 张图片'
        })

    except Exception as e:
        logger.error(f"获取产品图片失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': '获取产品图片失败，请稍后重试'
        }), 500

@product_test_query_bp.route('/api/product-test-query/images/<serial_number>/file/<filename>', methods=['GET'])
def get_product_image_file(serial_number, filename):
    """获取具体的图片文件"""
    try:
        import os
        from flask import send_file, abort
        from werkzeug.utils import secure_filename

        # 安全处理文件名
        safe_filename = secure_filename(filename)
        if not safe_filename:
            abort(404)

        # 构建文件路径
        image_dir = os.path.join(PRODUCT_IMAGES_BASE_PATH, serial_number)
        file_path = os.path.join(image_dir, safe_filename)

        # 检查文件是否存在且在允许的目录内
        if not os.path.exists(file_path) or not os.path.isfile(file_path):
            abort(404)

        # 检查路径安全性（防止路径遍历攻击）
        if not os.path.abspath(file_path).startswith(os.path.abspath(image_dir)):
            abort(403)

        # 返回文件
        return send_file(file_path)

    except Exception as e:
        logger.error(f"获取图片文件失败: {str(e)}")
        abort(500)
