# 成品测试查询 - 产品图片查看功能

## 功能概述

在成品测试查询页面的详情模态框中，为设备信息部分添加了"图片查看"按钮，用户可以通过SN号查看对应产品的相关图片。

## 功能特点

### 1. 图片查看入口
- 位置：详情模态框 → 设备信息卡片
- 触发：点击"图片查看"按钮
- 支持：CPU控制器、耦合器、IO模块等所有产品类型

### 2. 图片展示功能
- **网格布局**：自适应网格显示，每行最多显示多张图片
- **图片预览**：点击图片可全屏预览
- **图片信息**：显示文件名、上传时间、文件大小
- **图片下载**：支持单张图片下载

### 3. 交互体验
- **响应式设计**：支持桌面和移动设备
- **键盘操作**：ESC键关闭预览
- **拖拽支持**：模态框可拖拽移动
- **错误处理**：图片加载失败时显示占位符

## 技术实现

### 前端实现

#### 1. 按钮添加
在设备信息HTML中添加图片查看按钮：
```html
<button class="btn btn-primary" onclick="viewProductImages('${serialNumber}')" 
        style="font-size: 12px; padding: 6px 12px;">
    <i class="fas fa-images"></i> 图片查看
</button>
```

#### 2. 图片模态框
```html
<div id="product-images-modal" class="product-test-query__modal">
    <div class="product-test-query__modal-content">
        <div class="product-test-query__modal-header">
            <h2 class="product-test-query__modal-title">产品图片</h2>
            <span class="product-test-query__modal-close">&times;</span>
        </div>
        <div class="product-test-query__modal-body">
            <div id="product-images-content">
                <!-- 图片内容动态加载 -->
            </div>
        </div>
    </div>
</div>
```

#### 3. 核心JavaScript函数
- `viewProductImages(serialNumber)` - 获取并显示图片
- `showProductImagesModal(images, serialNumber)` - 显示图片模态框
- `previewImage(imageUrl, imageName)` - 全屏预览图片
- `downloadImage(imageUrl, fileName)` - 下载图片

### 后端实现

#### 1. API路由
```python
# 获取产品图片列表
@product_test_query_bp.route('/api/product-test-query/images/<serial_number>', methods=['GET'])
def get_product_images(serial_number):
    # 返回指定SN号的图片列表

# 获取具体图片文件
@product_test_query_bp.route('/api/product-test-query/images/<serial_number>/file/<filename>', methods=['GET'])
def get_product_image_file(serial_number, filename):
    # 返回具体的图片文件
```

#### 2. 图片存储结构
```
/var/data/product_images/
├── HSE2517001001/          # SN号目录
│   ├── 产品正面图.jpg
│   ├── 产品背面图.jpg
│   ├── 测试结果图.png
│   └── 包装图.jpg
├── HSE2517001002/
│   └── ...
└── ...
```

## 部署配置

### 1. 目录创建
运行设置脚本创建必要的目录结构：
```bash
python3 setup_product_images.py
```

### 2. 配置文件
在 `config.py` 中添加图片存储路径配置：
```python
# 产品图片存储配置
PRODUCT_IMAGES_BASE_PATH = os.environ.get('PRODUCT_IMAGES_PATH', '/var/data/product_images')
```

### 3. 权限设置
确保应用有读取图片目录的权限：
```bash
sudo chown -R www-data:www-data /var/data/product_images
sudo chmod -R 755 /var/data/product_images
```

## 使用说明

### 1. 图片上传
管理员需要按以下规则组织图片：
- 在 `/var/data/product_images/` 下创建以SN号命名的目录
- 将对应产品的图片放入该目录
- 支持的图片格式：.jpg, .jpeg, .png, .gif, .bmp, .webp

### 2. 图片命名建议
- 使用描述性文件名：`产品正面图.jpg`、`测试结果.png`
- 避免特殊字符和空格
- 建议使用中文或英文描述

### 3. 用户操作流程
1. 在成品测试查询页面搜索产品
2. 双击SN号或点击详情按钮打开详情模态框
3. 在设备信息卡片中点击"图片查看"按钮
4. 在图片模态框中浏览、预览、下载图片

## 错误处理

### 1. 无图片情况
- 显示提示信息："该SN号暂无相关图片"
- 不会显示空的图片模态框

### 2. 图片加载失败
- 显示图片占位符
- 不影响其他图片的正常显示

### 3. 网络错误
- 显示错误提示："获取产品图片失败，请检查网络连接"
- 用户可重试操作

## 扩展功能

### 未来可扩展的功能：
1. **图片上传**：在详情页面直接上传图片
2. **图片分类**：按测试阶段、角度等分类显示
3. **图片标注**：支持在图片上添加标注信息
4. **批量操作**：批量下载、删除图片
5. **图片压缩**：自动压缩大图片以提高加载速度

## 注意事项

1. **存储空间**：定期清理不需要的图片文件
2. **文件安全**：确保图片文件路径安全，防止路径遍历攻击
3. **性能优化**：大量图片时考虑分页加载
4. **备份策略**：重要图片文件需要定期备份
