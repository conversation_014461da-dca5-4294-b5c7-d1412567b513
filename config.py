# config.py
import os

# JWT密钥，用于Token的编码和解码
# 可以通过环境变量覆写保证安全性
JWT_SECRET = os.environ.get('JWT_SECRET', 'your-jwt-secret')

# 固件文件上传配置
FIRMWARE_UPLOAD_BASE_PATH = os.environ.get('FIRMWARE_UPLOAD_PATH', '/var/data/firmware')

# 产品图片存储配置
PRODUCT_IMAGES_BASE_PATH = os.environ.get('PRODUCT_IMAGES_PATH', '/var/data/product_images')

# 确保上传目录存在
os.makedirs(FIRMWARE_UPLOAD_BASE_PATH, exist_ok=True)
os.makedirs(PRODUCT_IMAGES_BASE_PATH, exist_ok=True)